// OpenAI Service for MiCA Therapy Simulation
import OpenAI from 'openai';
import { OpenAIRequest, AgentResponse } from '../types/index.js';

export class OpenAIService {
  private client: OpenAI;
  private defaultModel: string;
  private defaultTemperature: number;
  private defaultMaxTokens: number;

  constructor() {
    const apiKey = process.env['OPENAI_API_KEY'];
    if (!apiKey) {
      throw new Error('OPENAI_API_KEY environment variable is required');
    }

    this.client = new OpenAI({
      apiKey: apiKey,
    });

    this.defaultModel = process.env['OPENAI_MODEL'] || 'gpt-4o-mini';
    this.defaultTemperature = parseFloat(process.env['OPENAI_TEMPERATURE'] || '0.7');
    this.defaultMaxTokens = parseInt(process.env['OPENAI_MAX_TOKENS'] || '500');

    console.log(`🤖 OpenAI Service initialized with model: ${this.defaultModel}`);
  }

  /**
   * Generate a response from OpenAI
   */
  async generateResponse(request: OpenAIRequest): Promise<AgentResponse> {
    const startTime = Date.now();
    
    try {
      console.log(`🔄 Generating OpenAI response with model: ${this.defaultModel}`);
      console.log(`📝 Messages count: ${request.messages.length}`);
      console.log(`🌡️ Temperature: ${request.temperature}`);
      
      const completion = await this.client.chat.completions.create({
        model: this.defaultModel,
        messages: request.messages,
        temperature: request.temperature || this.defaultTemperature,
        max_completion_tokens: request.max_completion_tokens || this.defaultMaxTokens,
      });

      const processingTime = Date.now() - startTime;
      const responseContent = completion.choices[0]?.message?.content || '';

      console.log(`✅ OpenAI response generated in ${processingTime}ms`);
      console.log(`📊 Usage: ${completion.usage?.total_tokens} tokens`);

      return {
        message: responseContent,
        thinking: '', // Will be populated by agent services
        metadata: {
          confidence: this.calculateConfidence(completion),
          processingTime,
          sentiment: 'neutral', // Will be analyzed by agent services
          motivationLevel: 'medium', // Will be analyzed by agent services
          engagementLevel: 'medium', // Will be analyzed by agent services
        }
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;
      console.error('❌ OpenAI API error:', error);
      
      // Handle different types of errors
      if (error instanceof Error) {
        if (error.message.includes('rate limit')) {
          throw new Error('OpenAI rate limit exceeded. Please try again later.');
        } else if (error.message.includes('insufficient_quota')) {
          throw new Error('OpenAI quota exceeded. Please check your billing.');
        } else if (error.message.includes('invalid_api_key')) {
          throw new Error('Invalid OpenAI API key. Please check your configuration.');
        }
      }

      return {
        message: 'I apologize, but I\'m having trouble processing your message right now. Could you please try again?',
        thinking: 'Error occurred while generating response',
        metadata: {
          confidence: 0,
          processingTime,
          sentiment: 'neutral',
          motivationLevel: 'medium',
          engagementLevel: 'medium',
        }
      };
    }
  }

  /**
   * Generate a thinking process for an agent
   */
  async generateThinking(
    agentType: 'therapist' | 'patient',
    context: string,
    prompt: string
  ): Promise<string> {
    try {
      console.log(`🧠 Generating thinking process for ${agentType}`);
      
      const completion = await this.client.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: 'system',
            content: `You are generating internal thoughts for a ${agentType} in a therapy simulation. Provide realistic internal thoughts, considerations, and reasoning processes.`
          },
          {
            role: 'user',
            content: `Context: ${context}\n\nPrompt: ${prompt}`
          }
        ],
        temperature: 0.8,
        max_completion_tokens: 200,
      });

      const thinking = completion.choices[0]?.message?.content || 'Processing...';
      console.log(`💭 Thinking generated for ${agentType}: ${thinking.substring(0, 100)}...`);
      
      return thinking;

    } catch (error) {
      console.error(`❌ Error generating thinking for ${agentType}:`, error);
      return `${agentType === 'therapist' ? 'Analyzing patient response and considering therapeutic approach...' : 'Processing therapist\'s words and formulating response...'}`;
    }
  }

  /**
   * Analyze sentiment of a message
   */
  async analyzeSentiment(message: string): Promise<'positive' | 'negative' | 'neutral'> {
    try {
      console.log('📊 Analyzing sentiment...');
      
      const completion = await this.client.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: 'system',
            content: 'Analyze the sentiment of the following message. Respond with only one word: positive, negative, or neutral.'
          },
          {
            role: 'user',
            content: message
          }
        ],
        temperature: 0.1,
        max_completion_tokens: 10,
      });

      const sentiment = completion.choices[0]?.message?.content?.toLowerCase().trim();
      
      if (sentiment === 'positive' || sentiment === 'negative' || sentiment === 'neutral') {
        console.log(`📈 Sentiment analyzed: ${sentiment}`);
        return sentiment;
      }
      
      return 'neutral';

    } catch (error) {
      console.error('❌ Error analyzing sentiment:', error);
      return 'neutral';
    }
  }

  /**
   * Analyze engagement level
   */
  async analyzeEngagement(message: string): Promise<'low' | 'medium' | 'high'> {
    try {
      console.log('📊 Analyzing engagement level...');
      
      const completion = await this.client.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: 'system',
            content: 'Analyze the engagement level of this message in a therapy context. Consider factors like detail, emotional expression, and willingness to share. Respond with only one word: low, medium, or high.'
          },
          {
            role: 'user',
            content: message
          }
        ],
        temperature: 0.1,
        max_completion_tokens: 10,
      });

      const engagement = completion.choices[0]?.message?.content?.toLowerCase().trim();
      
      if (engagement === 'low' || engagement === 'medium' || engagement === 'high') {
        console.log(`📊 Engagement analyzed: ${engagement}`);
        return engagement;
      }
      
      return 'medium';

    } catch (error) {
      console.error('❌ Error analyzing engagement:', error);
      return 'medium';
    }
  }

  /**
   * Analyze motivation level
   */
  async analyzeMotivation(message: string): Promise<'low' | 'medium' | 'high'> {
    try {
      console.log('📊 Analyzing motivation level...');
      
      const completion = await this.client.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: 'system',
            content: 'Analyze the motivation level for change and therapy engagement in this message. Consider willingness to work on issues, hope for improvement, and commitment to the process. Respond with only one word: low, medium, or high.'
          },
          {
            role: 'user',
            content: message
          }
        ],
        temperature: 0.1,
        max_completion_tokens: 10,
      });

      const motivation = completion.choices[0]?.message?.content?.toLowerCase().trim();
      
      if (motivation === 'low' || motivation === 'medium' || motivation === 'high') {
        console.log(`🎯 Motivation analyzed: ${motivation}`);
        return motivation;
      }
      
      return 'medium';

    } catch (error) {
      console.error('❌ Error analyzing motivation:', error);
      return 'medium';
    }
  }

  /**
   * Calculate confidence score based on OpenAI response
   */
  private calculateConfidence(completion: OpenAI.Chat.Completions.ChatCompletion): number {
    // Simple confidence calculation based on response characteristics
    const choice = completion.choices[0];
    if (!choice) return 0;

    let confidence = 0.7; // Base confidence

    // Adjust based on finish reason
    if (choice.finish_reason === 'stop') {
      confidence += 0.2;
    } else if (choice.finish_reason === 'length') {
      confidence -= 0.1;
    }

    // Adjust based on response length (very short or very long responses might be less confident)
    const messageLength = choice.message?.content?.length || 0;
    if (messageLength > 50 && messageLength < 500) {
      confidence += 0.1;
    }

    return Math.max(0, Math.min(1, confidence));
  }

  /**
   * Test the OpenAI connection
   */
  async testConnection(): Promise<boolean> {
    try {
      console.log('🔍 Testing OpenAI connection...');
      
      const completion = await this.client.chat.completions.create({
        model: this.defaultModel,
        messages: [{ role: 'user', content: 'Hello' }],
        max_completion_tokens: 5,
      });

      const success = !!completion.choices[0]?.message?.content;
      console.log(success ? '✅ OpenAI connection successful' : '❌ OpenAI connection failed');
      
      return success;

    } catch (error) {
      console.error('❌ OpenAI connection test failed:', error);
      return false;
    }
  }
}
