<script lang="ts">
  import { onMount } from 'svelte';
  import ThemeToggle from '$components/ui/ThemeToggle.svelte';

  let wsConnection: WebSocket | null = null;
  let connectionStatus = 'Disconnected';
  let messages: Array<{
    sender: 'therapist' | 'patient';
    content: string;
    timestamp: string;
    thinking?: string;
    metadata?: any;
  }> = [];
  let therapistThoughts: Array<{ content: string; timestamp: string }> = [];
  let patientThoughts: Array<{ content: string; timestamp: string }> = [];

  // Dummy messages for testing
  // messages = [
  //   {
  //     sender: 'therapist',
  //     content: 'Test Message',
  //     timestamp: new Date().toISOString(),
  //     thinking: 'Test Thinking',
  //     metadata: {
  //       sentiment: 'positive',
  //       motivationLevel: 'high',
  //       engagementLevel: 'high',
  //       confidence: 0.9,
  //       processingTime: 0
  //     }
  //   },
  //   {
  //     sender: 'patient',
  //     content: 'Test Message',
  //     timestamp: new Date().toISOString(),
  //     thinking: 'Test Thinking',
  //     metadata: {
  //       sentiment: 'positive',
  //       motivationLevel: 'high',
  //       engagementLevel: 'high',
  //       confidence: 0.9,
  //       processingTime: 0
  //     }
  //   }
  // ];
  // therapistThoughts = [
  //   {
  //     content: 'Test Thinking',
  //     timestamp: new Date().toISOString()
  //   }
  // ];
  // patientThoughts = [
  //   {
  //     content: 'Test Thinking',
  //     timestamp: new Date().toISOString()
  //   }
  // ];
  
  // Configuration state
  let maxTurns = 20;
  let conversationActive = false;
  let conversationId: string | null = null;
  
  onMount(() => {
    connectWebSocket();
    
    return () => {
      if (wsConnection) {
        wsConnection.close();
      }
    };
  });
  
  function connectWebSocket() {
    const wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:3000';
    wsConnection = new WebSocket(`${wsUrl}/ws`);
    
    wsConnection.onopen = () => {
      connectionStatus = 'Connected';
      console.log('🔌 WebSocket connected successfully');
    };

    wsConnection.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log('📨 Received WebSocket message:', data);

        handleWebSocketMessage(data);
      } catch (error) {
        console.error('❌ Error parsing WebSocket message:', error);
      }
    };
    
    wsConnection.onclose = () => {
      connectionStatus = 'Disconnected';
      console.log('WebSocket disconnected');
    };
    
    wsConnection.onerror = (error) => {
      connectionStatus = 'error';
      console.error('WebSocket error:', error);
    };
  }
  
  function handleWebSocketMessage(data: any) {
    console.log(`🔄 Handling message type: ${data.type}`);

    switch (data.type) {
      case 'welcome':
        console.log('👋 Welcome message received');
        break;

      case 'conversation_created':
        console.log('✅ Conversation created:', data.data);
        conversationId = data.data.conversationId;
        break;

      case 'conversation_started':
        console.log('🚀 Conversation started:', data.data);
        break;

      case 'conversation_message':
        console.log('💬 Conversation message received:', data.data);
        handleConversationMessage(data.data);
        break;

      case 'conversation_ended':
        console.log('🏁 Conversation ended:', data.data);
        conversationActive = false;
        break;

      case 'conversation_paused':
        console.log('⏸️ Conversation paused');
        break;

      case 'conversation_resumed':
        console.log('▶️ Conversation resumed');
        break;

      case 'conversation_cleared':
        console.log('🗑️ Conversation cleared');
        messages = [];
        therapistThoughts = [];
        patientThoughts = [];
        break;

      case 'error':
        console.error('❌ Server error:', data.message);
        break;

      default:
        console.warn('⚠️ Unknown message type:', data.type);
        // For backward compatibility, treat unknown messages as general messages
        if (data.message) {
          messages = [...messages, {
            sender: 'therapist', // Default sender
            content: data.message,
            timestamp: data.timestamp || new Date().toISOString()
          }];
        }
    }
  }

  function handleConversationMessage(messageData: any) {
    console.log('📝 Processing conversation message:', messageData);

    if (messageData.message) {
      // Add conversation message
      messages = [...messages, {
        sender: messageData.message.sender,
        content: messageData.message.content,
        timestamp: messageData.message.timestamp,
        thinking: messageData.thinking?.content,
        metadata: messageData.metadata
      }];

      console.log(`💭 ${messageData.message.sender} said: "${messageData.message.content}"`);
    }

    if (messageData.thinking) {
      // Add thinking to appropriate array
      const thought = {
        content: messageData.thinking.content,
        timestamp: messageData.thinking.timestamp
      };

      if (messageData.thinking.agent === 'therapist') {
        therapistThoughts = [...therapistThoughts, thought];
        console.log(`🧠 Therapist thinking: "${thought.content}"`);
      } else if (messageData.thinking.agent === 'patient') {
        patientThoughts = [...patientThoughts, thought];
        console.log(`💭 Patient thinking: "${thought.content}"`);
      }
    }
  }

  function startConversation() {
    console.log('🚀 Starting conversation...');
    console.log(`⚙️ Configuration: maxTurns=${maxTurns}`);

    if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
      conversationActive = true;

      const message = {
        type: 'start_conversation',
        config: { maxTurns }
      };

      console.log('📤 Sending start conversation message:', message);
      wsConnection.send(JSON.stringify(message));
    } else {
      console.error('❌ WebSocket not connected');
    }
  }

  function clearConversation() {
    console.log('🗑️ Clearing conversation...');

    if (wsConnection && wsConnection.readyState === WebSocket.OPEN && conversationId) {
      wsConnection.send(JSON.stringify({
        type: 'clear_conversation'
      }));
    }

    // Clear local state
    messages = [];
    therapistThoughts = [];
    patientThoughts = [];
    conversationActive = false;
    conversationId = null;
  }
</script>

<svelte:head>
  <title>MiCA</title>
</svelte:head>

<div class="min-h-screen bg-neutral-50 dark:bg-neutral-900 theme-transition">
  <!-- Header -->
  <header class="bg-white dark:bg-neutral-800 shadow-sm border-b border-neutral-200 dark:border-neutral-700 theme-transition">
    <div class="mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <div class="flex items-center">
          <h1 class="text-2xl font-bold text-neutral-900 dark:text-neutral-100 theme-transition">MiCA</h1>
        </div>
        <div class="flex items-center space-x-4">
          <!-- Theme Toggle -->
          <ThemeToggle variant="button" size="md" />

          <!-- WebSocket Status -->
          <div class="flex items-center space-x-2">
            <span class="text-sm text-neutral-600 dark:text-neutral-400 theme-transition">WebSocket Status:</span>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {
              connectionStatus === 'Connected' ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' :
              connectionStatus === 'Error' ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' :
              'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'
            } theme-transition">
              {connectionStatus}
            </span>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- Configuration Panel -->
  <div class="bg-white dark:bg-neutral-800 border-b border-neutral-200 dark:border-neutral-700 theme-transition">
    <div class="mx-auto px-4 sm:px-6 lg:px-8 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-6">
          <div class="flex items-center space-x-2">
            <label for="maxTurns" class="label">Max Turns:</label>
            <input
              id="maxTurns"
              type="number"
              bind:value={maxTurns}
              min="1"
              max="100"
              class="input w-20"
              disabled={conversationActive}
            />
          </div>
        </div>
        <div class="flex items-center space-x-2">
          <button
            on:click={startConversation}
            disabled={conversationActive || connectionStatus !== 'Connected'}
            class="btn btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {conversationActive ? 'Running...' : 'Start'}
          </button>
          <button
            on:click={clearConversation}
            disabled={conversationActive}
            class="btn btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Clear
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content - Three Panes -->
  <div class="mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-400px)]">
      
      <!-- Left Pane: Therapist Thinking -->
      <div class="card p-6">
        <h2 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-4 flex items-center theme-transition">
          Therapist Thinking
        </h2>
        <div class="space-y-3 h-full overflow-y-auto">
          {#each therapistThoughts as thought}
            <div class="thinking-therapist dark:bg-primary-900/20 dark:text-blue-100 p-3 rounded-lg theme-transition">
              <p class="text-sm">{thought.content}</p>
              <hr class="border-neutral-300 dark:border-neutral-600 my-2 theme-transition" />
              <div class="mt-2 text-xs">
                <span class="font-medium">Time:</span> {new Date(thought.timestamp).toLocaleTimeString()}
              </div>
            </div>
          {/each}

          {#if therapistThoughts.length === 0}
            <div class="flex items-center justify-center h-full text-neutral-500 dark:text-neutral-400 theme-transition">
              <p class="text-sm">Therapist thinking will appear here during conversation...</p>
            </div>
          {/if}
        </div>
      </div>

      <!-- Middle Pane: Conversation -->
      <div class="card p-6">
        <h2 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-4 flex items-center theme-transition">
          Conversation
        </h2>
        <!-- Conversation Messages -->
        <div class="space-y-4 h-full overflow-y-auto">
          {#each messages as message}
            <div class="flex flex-col space-y-1">
              <div class="text-xs text-neutral-500 dark:text-neutral-400 flex items-center space-x-2 theme-transition">
                <span class="font-medium capitalize {message.sender === 'therapist' ? 'text-primary-600 dark:text-primary-400' : 'text-secondary-600 dark:text-secondary-400'} theme-transition">
                  {message.sender === 'therapist' ? '👩‍⚕️ Dr. Chen' : '👤 Alex'}
                </span>
                <span>•</span>
                <span>{new Date(message.timestamp).toLocaleTimeString()}</span>
              </div>
              <div class="message-bubble {message.sender === 'therapist' ? 'bg-primary-50 dark:bg-primary-900/20 border-l-4 border-primary-500' : 'bg-secondary-50 dark:bg-secondary-900/20 border-l-4 border-secondary-500'} text-neutral-900 dark:text-neutral-100 p-3 rounded-r-lg theme-transition">
                {message.content}
                {#if message.metadata}
                  <!-- Divider -->
                  <hr class="border-neutral-300 dark:border-neutral-600 my-2 theme-transition" />

                  <span class="text-xs font-bold">Sentiment: </span>
                  <span class="text-xs px-2 py-1 rounded uppercase {
                    message.metadata.sentiment === 'positive' ? 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300' :
                    message.metadata.sentiment === 'negative' ? 'bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300' :
                    'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300'
                  } theme-transition">
                    {message.metadata.sentiment}
                  </span>

                  <span class="ml-1 text-xs font-bold">Motivation: </span>
                  <span
                    class="text-xs px-2 py-1 rounded uppercase {
                      message.metadata.motivationLevel === 'low' ? 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-700 dark:text-yellow-300' :
                      message.metadata.motivationLevel === 'medium' ? 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300' :
                      'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300'
                    } theme-transition">
                    {message.metadata.motivationLevel}
                  </span>

                  <span class="ml-1 text-xs font-bold">Engagement: </span>
                  <span
                    class="text-xs px-2 py-1 rounded uppercase {
                      message.metadata.engagementLevel === 'low' ? 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-700 dark:text-yellow-300' :
                      message.metadata.engagementLevel === 'medium' ? 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300' :
                      'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300'
                    } theme-transition">
                    {message.metadata.engagementLevel}
                  </span>
                {/if}
              </div>
            </div>
          {/each}

          <!-- Show spinner while conversationActive = true -->
          {#if conversationActive}
            <div role="status" class="text-center">
              <svg aria-hidden="true" class="inline w-6 h-6 text-neutral-300 dark:text-neutral-600 animate-spin fill-primary-600 dark:fill-primary-400 theme-transition" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
                  <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
              </svg>
              <span class="sr-only">Loading...</span>
            </div>
          {:else if messages.length === 0}
            <div class="flex items-center justify-center h-full text-sm text-neutral-500 dark:text-neutral-400 theme-transition">
              <p>Click "Start" to begin...</p>
            </div>
          {/if}
        </div>
      </div>

      <!-- Right Pane: Patient Thinking -->
      <div class="card p-6">
        <h2 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-4 flex items-center theme-transition">
          Patient Thinking
        </h2>
        <div class="space-y-3 h-full overflow-y-auto">
          {#each patientThoughts as thought}
            <div class="thinking-patient dark:bg-secondary-900/20 dark:text-purple-100 p-3 rounded-lg theme-transition">
              <p class="text-sm">{thought.content}</p>
              <hr class="border-neutral-300 dark:border-neutral-600 my-2 theme-transition" />
              <div class="mt-2 text-xs">
                <span class="font-medium">Time:</span> {new Date(thought.timestamp).toLocaleTimeString()}
              </div>
            </div>
          {/each}

          {#if patientThoughts.length === 0}
            <div class="flex items-center justify-center h-full text-neutral-500 dark:text-neutral-400 theme-transition">
              <p class="text-sm">Patient thinking will appear here during conversation...</p>
            </div>
          {/if}
        </div>
      </div>
      
    </div>
  </div>
  <div class="mx-auto px-4 sm:px-6 lg:px-8">
    <div class="card p-6 h-44">
      <h2 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-4 flex items-center theme-transition">
          Observer
        </h2>
    </div>
  </div>
</div>
